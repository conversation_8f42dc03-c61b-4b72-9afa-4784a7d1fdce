using Microsoft.Extensions.Options;
using System.Text;
using Vault.MCP.Shared.DTOs;
using Vault.MCP.Shared.Extensions;
using Vault.MCP.Shared.Interfaces;
using Vault.MCP.Shared.Models;

namespace Vault.MCP.Client.Services;

public class McpClientService : IMcpClientService
{
    private readonly HttpClient _httpClient;
    private readonly IAiService _aiService;
    private readonly ISessionService _sessionService;
    private readonly ILogger<McpClientService> _logger;
    private readonly McpServerConfiguration _config;

    public McpClientService(
        IHttpClientFactory httpClientFactory, 
        IAiService aiService,
        ISessionService sessionService,
        ILogger<McpClientService> logger, 
        IOptions<McpServerConfiguration> config)
    {
        _httpClient = httpClientFactory.CreateClient("mcp-server");
        _aiService = aiService;
        _sessionService = sessionService;
        _logger = logger;
        _config = config.Value;
    }

    public async Task<ChatResponseDto> ProcessChatRequestAsync(ChatRequestDto request)
    {
        var sessionId = request.SessionId ?? Guid.NewGuid().ToString();
        
        try
        {
            _logger.LogInformation("Processing chat request for session {SessionId}: {Message}", sessionId, request.Message);

            // Get available tools
            var tools = await GetAvailableToolsAsync();
            if (!tools.Any())
            {
                return new ChatResponseDto
                {
                    Reply = "I'm sorry, but no tools are currently available to help with your request.",
                    SessionId = sessionId,
                    Success = false,
                    Error = "No tools available"
                };
            }

            // Generate execution plan using intelligent local planning
            var plan = GenerateIntelligentPlan(request.Message, tools);

            if (!plan.Any())
            {
                return new ChatResponseDto
                {
                    Reply = "I'm not sure how to help with that request. Could you please rephrase or be more specific?",
                    SessionId = sessionId,
                    Success = false,
                    Error = "No execution plan generated"
                };
            }

            // Execute plan steps
            var toolResults = new List<object>();
            var toolsUsed = new List<string>();

            foreach (var step in plan)
            {
                try
                {
                    _logger.LogDebug("Executing step: {Tool} - {Description}", step.Tool, step.Description);
                    
                    var toolRequest = new ToolRequestDto
                    {
                        Tool = step.Tool,
                        Params = step.Params
                    };

                    var result = await ExecuteToolAsync(toolRequest);
                    toolResults.Add(result);
                    toolsUsed.Add(step.Tool);

                    if (!result.Success)
                    {
                        _logger.LogWarning("Tool execution failed: {Tool} - {Error}", step.Tool, result.Error);
                        // Continue with other steps, but note the failure
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error executing tool {Tool}", step.Tool);
                    toolResults.Add(new ToolResponseDto
                    {
                        Success = false,
                        Error = $"Tool execution failed: {ex.Message}",
                        Timestamp = DateTime.UtcNow
                    });
                }
            }

            // Generate intelligent final answer based on tool results
            var finalAnswer = GenerateIntelligentAnswer(request.Message, toolResults, toolsUsed);

            // Store session context
            await _sessionService.UpdateSessionContextAsync(sessionId, new
            {
                lastMessage = request.Message,
                toolsUsed = toolsUsed,
                timestamp = DateTime.UtcNow
            });

            var response = new ChatResponseDto
            {
                Reply = finalAnswer,
                SessionId = sessionId,
                ToolsUsed = toolsUsed,
                Success = true
            };

            _logger.LogInformation("Chat request processed successfully for session {SessionId}", sessionId);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing chat request for session {SessionId}", sessionId);
            return new ChatResponseDto
            {
                Reply = "I'm sorry, but I encountered an error while processing your request. Please try again.",
                SessionId = sessionId,
                Success = false,
                Error = ex.Message
            };
        }
    }

    public async Task<List<Tool>> GetAvailableToolsAsync()
    {
        try
        {
            _logger.LogDebug("Retrieving available tools from MCP server");
            var tools = await _httpClient.GetFromJsonAsync<List<Tool>>("/tools");
            
            if (tools == null)
            {
                _logger.LogWarning("MCP server returned null tools list");
                return new List<Tool>();
            }

            _logger.LogDebug("Retrieved {ToolCount} tools from MCP server", tools.Count);
            return tools;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while retrieving tools from MCP server");
            throw new InvalidOperationException($"Failed to connect to MCP server: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while retrieving tools from MCP server");
            throw new TimeoutException($"MCP server request timed out after {_config.TimeoutSeconds} seconds", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while retrieving tools from MCP server");
            throw;
        }
    }

    public async Task<ToolResponseDto> ExecuteToolAsync(ToolRequestDto request)
    {
        try
        {
            _logger.LogDebug("Executing tool {ToolName} on MCP server", request.Tool);
            
            var response = await _httpClient.PostAsJsonAsync<ToolRequestDto, ToolResponseDto>("/tool", request);
            
            if (response == null)
            {
                throw new InvalidOperationException("MCP server returned null response");
            }

            _logger.LogDebug("Tool {ToolName} executed with success: {Success}", request.Tool, response.Success);
            return response;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while executing tool {ToolName}", request.Tool);
            throw new InvalidOperationException($"Failed to connect to MCP server: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while executing tool {ToolName}", request.Tool);
            throw new TimeoutException($"MCP server request timed out after {_config.TimeoutSeconds} seconds", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error while executing tool {ToolName}", request.Tool);
            throw;
        }
    }

    private List<PlanStepDto> GenerateIntelligentPlan(string userMessage, List<Tool> availableTools)
    {
        var plan = new List<PlanStepDto>();
        var message = userMessage.ToLowerInvariant();

        _logger.LogInformation("Generating intelligent plan for: {UserMessage}", userMessage);

        // Time-related queries
        if (IsTimeQuery(message))
        {
            plan.Add(new PlanStepDto
            {
                Tool = "getTime",
                Params = new Dictionary<string, object>(),
                Description = "Get current server time"
            });
            return plan;
        }

        // File/Vault-related queries
        if (IsVaultQuery(message))
        {
            var vaultParams = ExtractVaultParameters(message);
            plan.Add(new PlanStepDto
            {
                Tool = "getVaultFiles",
                Params = vaultParams,
                Description = "Retrieve vault files based on user criteria"
            });
            return plan;
        }

        // Multi-step queries
        if (IsComprehensiveQuery(message))
        {
            // First get vault files
            plan.Add(new PlanStepDto
            {
                Tool = "getVaultFiles",
                Params = new Dictionary<string, object>
                {
                    { "vaultId", "117" },
                    { "limit", 10 }
                },
                Description = "Get comprehensive vault file listing"
            });

            // Then get current time for context
            plan.Add(new PlanStepDto
            {
                Tool = "getTime",
                Params = new Dictionary<string, object>(),
                Description = "Get current time for context"
            });
            return plan;
        }

        // Default: intelligent vault query
        plan.Add(new PlanStepDto
        {
            Tool = "getVaultFiles",
            Params = ExtractVaultParameters(message),
            Description = "Get vault files based on inferred user intent"
        });

        return plan;
    }

    private bool IsTimeQuery(string message)
    {
        var timeKeywords = new[] { "time", "clock", "when", "date", "now", "current", "today" };
        return timeKeywords.Any(keyword => message.Contains(keyword));
    }

    private bool IsVaultQuery(string message)
    {
        var vaultKeywords = new[] { "file", "vault", "cad", "drawing", "model", "document", "dwg", "ipt", "iam", "show", "list", "get", "find", "search" };
        return vaultKeywords.Any(keyword => message.Contains(keyword));
    }

    private bool IsComprehensiveQuery(string message)
    {
        var comprehensiveKeywords = new[] { "everything", "all", "complete", "full", "comprehensive", "overview", "summary", "status" };
        return comprehensiveKeywords.Any(keyword => message.Contains(keyword));
    }

    private Dictionary<string, object> ExtractVaultParameters(string message)
    {
        var parameters = new Dictionary<string, object>
        {
            { "vaultId", "117" },
            { "limit", 10 }
        };

        // Extract specific parameters from user message
        if (message.Contains("released") || message.Contains("release"))
        {
            parameters["option_releasedFilesOnly"] = true;
            parameters["filter_State"] = "Released";
        }

        if (message.Contains("work in progress") || message.Contains("wip") || message.Contains("draft"))
        {
            parameters["filter_State"] = "Work in Progress";
        }

        if (message.Contains("latest") || message.Contains("recent"))
        {
            parameters["option_latestOnly"] = true;
            parameters["sort"] = "ModifiedDate desc";
        }

        if (message.Contains("old") || message.Contains("oldest"))
        {
            parameters["sort"] = "CreatedDate asc";
        }

        // Extract numbers for limits
        var numbers = System.Text.RegularExpressions.Regex.Matches(message, @"\b(\d+)\b");
        if (numbers.Count > 0 && int.TryParse(numbers[0].Value, out var limit))
        {
            parameters["limit"] = Math.Min(Math.Max(limit, 1), 50); // Clamp between 1 and 50
        }

        // Extract vault ID if specified
        if (message.Contains("vault") && numbers.Count > 0)
        {
            foreach (System.Text.RegularExpressions.Match match in numbers)
            {
                if (int.TryParse(match.Value, out var vaultId) && vaultId > 100)
                {
                    parameters["vaultId"] = vaultId.ToString();
                    break;
                }
            }
        }

        return parameters;
    }

    private string GenerateIntelligentAnswer(string userMessage, List<object> toolResults, List<string> toolsUsed)
    {
        _logger.LogInformation("Generating intelligent answer for: {UserMessage} using tools: {Tools}", userMessage, string.Join(", ", toolsUsed));

        if (!toolResults.Any())
        {
            return GenerateNoResultsResponse(userMessage);
        }

        var response = new StringBuilder();
        var hasTimeData = false;
        var hasFileData = false;
        var fileCount = 0;
        var currentTime = "";

        // Analyze tool results
        foreach (var result in toolResults)
        {
            if (result is ToolResponseDto toolResponse && toolResponse.Success && toolResponse.Data != null)
            {
                var dataJson = System.Text.Json.JsonSerializer.Serialize(toolResponse.Data);
                var data = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(dataJson);

                if (data.TryGetProperty("time", out var timeElement))
                {
                    hasTimeData = true;
                    var time = DateTime.Parse(timeElement.GetString()!);
                    currentTime = time.ToString("MMMM dd, yyyy 'at' HH:mm:ss UTC");
                }
                else if (data.TryGetProperty("files", out var filesElement))
                {
                    hasFileData = true;
                    fileCount = filesElement.GetArrayLength();
                }
            }
        }

        // Generate contextual response based on user intent and results
        response.AppendLine(GenerateContextualGreeting(userMessage, hasTimeData, hasFileData, fileCount));
        response.AppendLine();

        // Process each tool result with intelligent formatting
        foreach (var result in toolResults)
        {
            if (result is ToolResponseDto toolResponse && toolResponse.Success && toolResponse.Data != null)
            {
                var dataJson = System.Text.Json.JsonSerializer.Serialize(toolResponse.Data);
                var data = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(dataJson);

                if (data.TryGetProperty("time", out var timeElement))
                {
                    FormatTimeResponse(response, timeElement, userMessage);
                }
                else if (data.TryGetProperty("files", out var filesElement))
                {
                    FormatFileResponse(response, filesElement, userMessage);
                }
            }
        }

        // Add contextual closing
        response.AppendLine();
        response.AppendLine(GenerateContextualClosing(userMessage, hasTimeData, hasFileData));

        return response.ToString().Trim();
    }

    private string GenerateNoResultsResponse(string userMessage)
    {
        var message = userMessage.ToLowerInvariant();

        if (IsTimeQuery(message))
        {
            return "I'm sorry, I couldn't retrieve the current time right now. The time service might be temporarily unavailable.";
        }

        if (IsVaultQuery(message))
        {
            return "I wasn't able to find any vault files matching your criteria. This could be because:\n" +
                   "• The vault is empty or inaccessible\n" +
                   "• Your search criteria were too specific\n" +
                   "• There might be a connection issue with the vault system\n\n" +
                   "Try asking for 'all files' or check if the vault system is available.";
        }

        return "I wasn't able to retrieve any information for your request. Please try rephrasing your question or ask about vault files or the current time.";
    }

    private string GenerateContextualGreeting(string userMessage, bool hasTimeData, bool hasFileData, int fileCount)
    {
        var message = userMessage.ToLowerInvariant();

        if (hasTimeData && hasFileData)
        {
            return $"I've gathered both time and vault information for you. Found {fileCount} files and the current time.";
        }

        if (hasTimeData)
        {
            if (message.Contains("time") || message.Contains("clock"))
            {
                return "Here's the current server time:";
            }
            return "I've retrieved the current time information:";
        }

        if (hasFileData)
        {
            if (fileCount == 0)
            {
                return "I searched the vault but didn't find any files matching your criteria.";
            }
            else if (fileCount == 1)
            {
                return "I found 1 file in the vault:";
            }
            else if (message.Contains("latest") || message.Contains("recent"))
            {
                return $"Here are the {fileCount} most recent files I found:";
            }
            else if (message.Contains("released"))
            {
                return $"I found {fileCount} released files:";
            }
            else
            {
                return $"I found {fileCount} files in the vault:";
            }
        }

        return "Here's what I found:";
    }

    private void FormatTimeResponse(StringBuilder response, System.Text.Json.JsonElement timeElement, string userMessage)
    {
        var time = DateTime.Parse(timeElement.GetString()!);
        var message = userMessage.ToLowerInvariant();

        if (message.Contains("date"))
        {
            response.AppendLine($"📅 **Date**: {time:MMMM dd, yyyy}");
            response.AppendLine($"🕐 **Time**: {time:HH:mm:ss UTC}");
        }
        else
        {
            response.AppendLine($"🕐 **Current Time**: {time:MMMM dd, yyyy 'at' HH:mm:ss UTC}");
        }

        // Add helpful context
        var dayOfWeek = time.DayOfWeek;
        response.AppendLine($"📆 It's {dayOfWeek}");
    }

    private void FormatFileResponse(StringBuilder response, System.Text.Json.JsonElement filesElement, string userMessage)
    {
        var files = filesElement.EnumerateArray().ToList();
        var message = userMessage.ToLowerInvariant();

        if (!files.Any())
        {
            response.AppendLine("📂 No files found matching your criteria.");
            return;
        }

        // Group files by state for better presentation
        var filesByState = files.GroupBy(f => f.GetProperty("state").GetString()).ToList();

        foreach (var stateGroup in filesByState.OrderBy(g => g.Key))
        {
            var state = stateGroup.Key;
            var stateFiles = stateGroup.ToList();

            if (filesByState.Count > 1)
            {
                response.AppendLine($"**{state} Files ({stateFiles.Count}):**");
            }

            foreach (var file in stateFiles.Take(15)) // Limit to 15 files per state
            {
                var name = file.GetProperty("name").GetString();
                var path = file.GetProperty("path").GetString();
                var modifiedDate = DateTime.Parse(file.GetProperty("modifiedDate").GetString()!);
                var size = file.GetProperty("size").GetInt64();

                var sizeStr = FormatFileSize(size);
                var dateStr = FormatRelativeDate(modifiedDate);

                if (message.Contains("detail") || message.Contains("full"))
                {
                    response.AppendLine($"📄 **{name}**");
                    response.AppendLine($"   📁 Path: {path}");
                    response.AppendLine($"   📊 Size: {sizeStr}");
                    response.AppendLine($"   📅 Modified: {dateStr}");
                    response.AppendLine();
                }
                else
                {
                    response.AppendLine($"📄 **{name}** ({state}) - {sizeStr}, modified {dateStr}");
                }
            }

            if (stateFiles.Count > 15)
            {
                response.AppendLine($"   ... and {stateFiles.Count - 15} more {state.ToLowerInvariant()} files");
            }
        }
    }

    private string GenerateContextualClosing(string userMessage, bool hasTimeData, bool hasFileData)
    {
        var message = userMessage.ToLowerInvariant();

        if (hasFileData && hasTimeData)
        {
            return "Is there anything specific about these files or do you need any other information?";
        }

        if (hasFileData)
        {
            if (message.Contains("latest") || message.Contains("recent"))
            {
                return "Would you like to see more files or get details about any specific file?";
            }
            return "Let me know if you need more details about any of these files or want to search with different criteria.";
        }

        if (hasTimeData)
        {
            return "Is there anything else you'd like to know?";
        }

        return "Feel free to ask about vault files, current time, or any other information you need.";
    }

    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private static string FormatRelativeDate(DateTime date)
    {
        var now = DateTime.UtcNow;
        var diff = now - date;

        if (diff.TotalDays < 1)
        {
            if (diff.TotalHours < 1)
                return $"{(int)diff.TotalMinutes} minutes ago";
            return $"{(int)diff.TotalHours} hours ago";
        }
        else if (diff.TotalDays < 7)
        {
            return $"{(int)diff.TotalDays} days ago";
        }
        else if (diff.TotalDays < 30)
        {
            return $"{(int)(diff.TotalDays / 7)} weeks ago";
        }
        else
        {
            return date.ToString("MMM dd, yyyy");
        }
    }
}
