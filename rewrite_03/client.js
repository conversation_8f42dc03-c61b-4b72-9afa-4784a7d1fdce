const express = require('express');
const cors = require('cors');
const axios = require('axios');

const app = express();
const PORT = 5098;

app.use(cors());
app.use(express.json());

const MCP_SERVER_URL = 'http://localhost:5001';
const AI_API_URL = 'https://api.epicai.fun/v1/chat/completions';
const AI_API_KEY = 'ecs-00';

// Main /ask endpoint
app.post('/ask', async (req, res) => {
  const { message } = req.body;
  
  if (!message) {
    return res.status(400).json({ error: 'Message is required' });
  }
  
  try {
    // Get available tools from MCP server
    const toolsResponse = await axios.get(`${MCP_SERVER_URL}/tools`);
    const tools = toolsResponse.data;
    
    // Create tool descriptions for AI
    const toolDescriptions = tools.map(tool => 
      `${tool.name}: ${tool.description}`
    ).join('\n');
    
    // Determine if we need to use tools
    const needsVaultData = message.toLowerCase().includes('vault') || 
                          message.toLowerCase().includes('file') ||
                          message.toLowerCase().includes('search');
    
    let context = '';
    
    if (needsVaultData) {
      // Try to get vault data
      try {
        if (message.toLowerCase().includes('search')) {
          // Extract search query from message
          const searchMatch = message.match(/search.*?for\s+["']?([^"']+)["']?/i) ||
                             message.match(/find.*?["']?([^"']+)["']?/i);
          const searchQuery = searchMatch ? searchMatch[1] : message;
          
          const toolResponse = await axios.post(`${MCP_SERVER_URL}/tool`, {
            tool: 'searchVaultFiles',
            params: { query: searchQuery, vaultId: '117' }
          });
          
          if (toolResponse.data.success) {
            context = `Search results: ${JSON.stringify(toolResponse.data.result, null, 2)}`;
          }
        } else {
          // Get all vault files
          const toolResponse = await axios.post(`${MCP_SERVER_URL}/tool`, {
            tool: 'getVaultFiles',
            params: { vaultId: '117' }
          });
          
          if (toolResponse.data.success) {
            context = `Vault files: ${JSON.stringify(toolResponse.data.result, null, 2)}`;
          }
        }
      } catch (toolError) {
        console.error('Tool execution error:', toolError.message);
        context = 'Unable to fetch vault data at the moment.';
      }
    }
    
    // Prepare AI request
    const aiMessages = [
      {
        role: 'system',
        content: `You are a helpful assistant for Autodesk Vault. You can help users with vault-related questions.
Available tools: ${toolDescriptions}
${context ? `Current vault data: ${context}` : ''}`
      },
      {
        role: 'user',
        content: message
      }
    ];
    
    // Call AI service
    const aiResponse = await axios.post(AI_API_URL, {
      model: 'gpt-3.5-turbo',
      messages: aiMessages,
      temperature: 0.7,
      max_tokens: 1000
    }, {
      headers: {
        'Authorization': `Bearer ${AI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    const reply = aiResponse.data.choices[0].message.content;
    
    res.json({ reply });
    
  } catch (error) {
    console.error('Error processing request:', error.message);
    res.status(500).json({ 
      error: 'Failed to process request',
      reply: 'Sorry, I encountered an error while processing your request. Please try again.'
    });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'mcp-client' });
});

app.listen(PORT, () => {
  console.log(`MCP Client running on http://localhost:${PORT}`);
});
