using System.Text.Json;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddHttpClient().ConfigurePrimaryHttpMessageHandler(() =>
{
    return new HttpClientHandler
    {
        ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
    };
});
builder.Services.AddCors();

var app = builder.Build();

app.UseCors(policy => policy.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());

const string AI_API_URL = "https://api.epicai.fun/v1/chat/completions";
const string AI_API_KEY = "ecs-00";
const string MCP_SERVER_URL = "http://localhost:5001";

app.MapPost("/ask", async (HttpContext context, IHttpClientFactory httpClientFactory) =>
{
    var httpClient = httpClientFactory.CreateClient();
    
    using var reader = new StreamReader(context.Request.Body);
    var body = await reader.ReadToEndAsync();
    var request = JsonSerializer.Deserialize<AskRequest>(body);
    
    if (string.IsNullOrEmpty(request?.Message))
    {
        return Results.BadRequest(new { error = "Message is required" });
    }
    
    try
    {
        // Get available tools
        var toolsResponse = await httpClient.GetStringAsync($"{MCP_SERVER_URL}/tools");
        var tools = JsonSerializer.Deserialize<Tool[]>(toolsResponse);
        
        // Use AI to determine which tools to use
        var planPrompt = $@"
User message: {request.Message}

Available tools:
{string.Join("\n", tools.Select(t => $"- {t.name}: {t.description}"))}

Respond with ONLY a JSON array of tool calls needed. Each tool call should have 'tool' and 'params' properties.
If no tools needed, respond with empty array [].

Examples:
- For ""how many files"": [{{""tool"":""getVaultFiles"",""params"":{{""vaultId"":""117""}}}}]
- For ""files modified in last 7 days"": [{{""tool"":""getTime"",""params"":{{}}}},{{""tool"":""getVaultFiles"",""params"":{{""vaultId"":""117""}}}}]
- For ""hello"": []
";

        var planResponse = await CallAI(httpClient, planPrompt);
        var toolCalls = JsonSerializer.Deserialize<ToolCall[]>(planResponse.Trim()) ?? Array.Empty<ToolCall>();
        
        // Execute tools
        var toolResults = new List<object>();
        foreach (var toolCall in toolCalls)
        {
            var toolRequest = new { tool = toolCall.tool, @params = toolCall.@params };
            var toolJson = JsonSerializer.Serialize(toolRequest);
            var content = new StringContent(toolJson, Encoding.UTF8, "application/json");
            
            var toolResponse = await httpClient.PostAsync($"{MCP_SERVER_URL}/tool", content);
            var toolResult = await toolResponse.Content.ReadAsStringAsync();
            toolResults.Add(JsonSerializer.Deserialize<object>(toolResult));
        }
        
        // Generate final response
        var finalPrompt = $@"
User asked: {request.Message}

Tool results: {JsonSerializer.Serialize(toolResults)}

Provide a helpful, natural response to the user based on the tool results. Be concise and friendly.
";

        var finalResponse = await CallAI(httpClient, finalPrompt);
        
        return Results.Ok(new { reply = finalResponse });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error: {ex.Message}");
    }
});

app.MapGet("/health", () => new { status = "ok", service = "mcp-client" });

app.Run("http://localhost:5098");

async Task<string> CallAI(HttpClient httpClient, string prompt)
{
    var aiRequest = new
    {
        model = "gpt-3.5-turbo",
        messages = new[]
        {
            new { role = "user", content = prompt }
        },
        temperature = 0.1,
        max_tokens = 1000
    };
    
    var json = JsonSerializer.Serialize(aiRequest);
    var content = new StringContent(json, Encoding.UTF8, "application/json");
    
    httpClient.DefaultRequestHeaders.Clear();
    httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {AI_API_KEY}");
    
    var response = await httpClient.PostAsync(AI_API_URL, content);
    var responseJson = await response.Content.ReadAsStringAsync();
    var aiResponse = JsonSerializer.Deserialize<AiResponse>(responseJson);
    
    return aiResponse.choices[0].message.content;
}

public record AskRequest(string Message);
public record Tool(string Name, string Description);
public record ToolCall(string Tool, Dictionary<string, object> Params);
public record AiResponse(Choice[] Choices);
public record Choice(Message Message);
public record Message(string Content);
