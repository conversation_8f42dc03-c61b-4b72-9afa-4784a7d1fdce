using System.Text.Json;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddHttpClient("default").ConfigurePrimaryHttpMessageHandler(() =>
{
    return new HttpClientHandler
    {
        ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
    };
});
builder.Services.AddCors();

var app = builder.Build();

app.UseCors(policy => policy.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());

const string AI_API_URL = "https://api.epicai.fun/v1/chat/completions";
const string AI_API_KEY = "ecs-00";
const string MCP_SERVER_URL = "http://localhost:5001";

app.MapPost("/ask", async (AskRequest request, IHttpClientFactory httpClientFactory) =>
{
    var httpClient = httpClientFactory.CreateClient("default");
    
    if (string.IsNullOrEmpty(request.Message))
    {
        return Results.BadRequest(new { error = "Message is required" });
    }
    
    try
    {
        // Simple logic without complex AI planning
        var needsVaultData = request.Message.ToLower().Contains("file") ||
                            request.Message.ToLower().Contains("vault") ||
                            request.Message.ToLower().Contains("how many");

        var context = "";

        if (needsVaultData)
        {
            try
            {
                // Get vault files
                var toolRequest = new { tool = "getVaultFiles", @params = new { vaultId = "117" } };
                var toolJson = JsonSerializer.Serialize(toolRequest);
                var content = new StringContent(toolJson, Encoding.UTF8, "application/json");

                var toolResponse = await httpClient.PostAsync($"{MCP_SERVER_URL}/tool", content);
                var toolResult = await toolResponse.Content.ReadAsStringAsync();
                context = $"Vault data: {toolResult}";
            }
            catch (Exception ex)
            {
                context = $"Could not fetch vault data: {ex.Message}";
            }
        }

        // Generate response with context
        var prompt = $@"
User asked: {request.Message}

{context}

Provide a helpful, natural response to the user. Be concise and friendly.
";

        var response = await CallAI(httpClient, prompt);

        return Results.Ok(new { reply = response });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error: {ex.Message}");
    }
});

app.MapGet("/health", () => new { status = "ok", service = "mcp-client" });

app.Run("http://localhost:5098");

async Task<string> CallAI(HttpClient httpClient, string prompt)
{
    try
    {
        var aiRequest = new
        {
            model = "gpt-3.5-turbo",
            messages = new[]
            {
                new { role = "user", content = prompt }
            },
            temperature = 0.1,
            max_tokens = 1000
        };

        var json = JsonSerializer.Serialize(aiRequest);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        httpClient.DefaultRequestHeaders.Clear();
        httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {AI_API_KEY}");

        var response = await httpClient.PostAsync(AI_API_URL, content);
        var responseJson = await response.Content.ReadAsStringAsync();

        Console.WriteLine($"AI API Response: {responseJson}");

        var aiResponse = JsonSerializer.Deserialize<AiResponse>(responseJson);

        return aiResponse?.Choices?[0]?.Message?.Content ?? "No response from AI";
    }
    catch (Exception ex)
    {
        Console.WriteLine($"AI Call Error: {ex.Message}");
        return "Sorry, I encountered an error while processing your request.";
    }
}

public record AskRequest(string Message);
public record Tool(string Name, string Description);
public record ToolCall(string Tool, Dictionary<string, object> Params);
public record AiResponse(Choice[] Choices);
public record Choice(Message Message);
public record Message(string Content);
