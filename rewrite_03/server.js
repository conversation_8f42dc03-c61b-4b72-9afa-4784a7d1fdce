const express = require('express');
const cors = require('cors');
const axios = require('axios');

const app = express();
const PORT = 5001;

app.use(cors());
app.use(express.json());

// Simple tools registry
const tools = [
  {
    name: "getTime",
    description: "Get current server time",
    parameters: {}
  },
  {
    name: "getVaultFiles",
    description: "Get files from vault",
    parameters: {
      vaultId: { type: "string", description: "Vault ID" }
    }
  },
  {
    name: "searchVaultFiles",
    description: "Search files in vault",
    parameters: {
      query: { type: "string", description: "Search query" },
      vaultId: { type: "string", description: "Vault ID" }
    }
  }
];

// Get available tools
app.get('/tools', (req, res) => {
  res.json(tools);
});

// Execute a tool
app.post('/tool', async (req, res) => {
  const { tool, params } = req.body;
  
  try {
    let result;
    
    switch (tool) {
      case 'getTime':
        result = { time: new Date().toISOString() };
        break;
        
      case 'getVaultFiles':
        const vaultId = params.vaultId || '117';
        try {
          const response = await axios.get(`http://localhost:4000/api/vault/${vaultId}/files`);
          result = response.data;
        } catch (error) {
          result = { error: 'Failed to fetch vault files', details: error.message };
        }
        break;
        
      case 'searchVaultFiles':
        const searchVaultId = params.vaultId || '117';
        const query = params.query || '';
        try {
          const response = await axios.get(`http://localhost:4000/api/vault/${searchVaultId}/files/search?q=${encodeURIComponent(query)}`);
          result = response.data;
        } catch (error) {
          result = { error: 'Failed to search vault files', details: error.message };
        }
        break;
        
      default:
        return res.status(400).json({ success: false, error: 'Unknown tool' });
    }
    
    res.json({ success: true, result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'mcp-server' });
});

app.listen(PORT, () => {
  console.log(`MCP Server running on http://localhost:${PORT}`);
});
